# -*- coding: utf-8 -*-
import os
import docx
import pandas as pd

# --- 1. 配置区域 ---

# 请将您的Word文档路径放在这里
WORD_FILE_PATH = "C:\Users\<USER>\Desktop\附件1.高速公路养护内业资料用表.doc"  # <--- 修改这里

# 指定输出Excel文件的文件夹
OUTPUT_DIR = "C:\Users\<USER>\Desktop\高速公路养护"

# --- 2. 解析您提供的对应关系 ---
# 我已经将您提供的VBA代码转换为了Python字典结构
EXCEL_SHEET_MAPPING = {
    "巡检、路况用表": ["路基损坏调查表", "沥青路面损坏调查表", "水泥混凝土路面损坏调查表", "桥隧构造物损坏调查表", "沿线设施损坏调查表", "板式橡胶支座更换前工作状况检查与检测记录表", "盆式支座更换前工作状况检查与检测记录表", "球形支座更换前工作状况检查与检测记录表", "伸缩装置更换前工作状况检查与检测记录表", "伸缩装置更换后初始技术状况检查记录表", "公路技术状况评定明细表", "公路技术状况评定汇总表", "路基病害调查与技术状况评定表", "路基病害类型汇总表", "路面技术状况评定汇总表", "桥梁评定指标检查评定表", "板拱桥、肋拱桥、箱形拱桥、双曲拱桥技术状况评定记录表", "刚架拱桥、桁架拱桥技术状况评定记录表", "钢-混凝土组合桥技术状况评定记录表", "斜拉桥技术状况评定记录表", "悬索桥技术状况评定记录表", "隧道土建结构技术状况评定表", "隧道其他工程设施技术状况评定表", "桥梁初始检查记录表", "桥梁日常巡查记录表（缆、索结构）", "桥梁日常巡查记录表（普通桥梁）", "桥梁定期检查记录表（梁式桥）", "桥梁定期检查记录表（板拱桥、肋拱桥、箱形拱桥、双曲拱桥）", "桥梁定期检查记录表（刚架拱桥、桁架拱桥）", "桥梁定期检查记录表（钢-混凝土组合桥）", "桥梁定期检查记录表（斜拉桥）", "桥梁定期检查记录表（悬索桥）", "桥梁经常检查记录表", "桥梁特殊检查记录表", "涵洞经常检查记录表", "涵洞定期检查记录表", "隧道土建结构日常巡查记录表", "通用巡查记录表", "隧道经常检查记录表", "隧道定期（特别）检查记录表"],
    "隧道检查情况展示表": ["×××高速公路基本情况一览表", "×××高速公路建设情况一览表", "×××高速公路桥梁一览表", "×××高速公路涵洞一览表", "×××高速公路隧道一览表", "×××高速公路互通立交一览表", "×××高速公路防撞护栏一览表", "×××高速公路交通标志一览表", "×××高速公路服务区一览表", "×××高速公路管理区、收费站、养护工区一览表", "×××高速公路沿线绿化一览表", "×××高速公路桥梁基本状况卡片", "×××高速公路涵洞基本状况卡片", "×××高速公路隧道基本状况卡片（__行）", "×××高速公路高边坡基本状况卡片（__行）", "×××高速公路高挡墙基本状况卡片（__行）"],
    "监理管理用表": ["开工令", "合同段开工报审表", "养护工程开工报审表", "养护单元开工报审表", "监理指令单", "监理指令回复单", "监理工程师通知单", "监理工程师通知回复单", "工程停工指令", "工程复工指令", "巡视记录", "旁站记录", "监理日志", "养护单元（中间）交工证书"],
    "质量管理用表": ["施工图审查报审表", "工程联系单", "养护工程任务通知单", "养护工程任务验收单", "XX工程量验收明细表", "工程划分报审表", "施工组织设计报审表", "施工技术（工艺试验）方案报审表", "首件工程认可申请单", "施工技术/安全交底记录", "主要进场设备报审表", "施工放样报验单", "检验申请批复单", "施工日志", "工程质量事故处理报告单", "工程复工报审表", "交工验收报审表"],
    "交竣工用表": ["公路养护工程交工验收报告", "公路养护工程交工验收合同段工程质量评分一览表", "养护工程竣（交）工验收委员会名单", "公路工程交工验收证书", "公路养护工程竣工验收报告单", "公路养护工程竣工资料检查表"],
    "合同管理用表": ["分包工程报审表", "进场人员资质报审表", "主要进场设备报审表", "主要设备退场报审表", "缺陷责任报审表", "工程缺陷责任终止证书", "中期支付证书", "中期支付证书（咨询类）", "中期支付审批表", "清单计量汇总表", "变更计量汇总表", "计量汇总表（含变更）", "清单中期计量表（不含变更）", "变更中期计量表", "中期计量汇总表（含变更）", "清单中间计量表", "变更中间计量表", "暂定金额支付报表", "奖罚台账", "支付报审表（咨询类）", "开工动员预付款报审表", "保留金支付报审表", "最终支付报审表", "工程变更令", "工程变更费用审批表", "工程变更费用初审意见表", "工程变更单价申报审批表", "工程单价编制说明", "现场工程量变更确认单", "工程变更方案审批表", "工程变更方案初审意见表", "工程变更方案会审单", "工程变更金额申报表", "工程变更金额估算表（跨年度）", "工程变更金额估算表（年度内）", "工程变更台账"],
    "安全管理用表": ["涉路施工内部审批表", "安全生产经费中间计量汇总表", "安全生产费用中期计量汇总表", "安全生产经费中间计量清单表", "安全生产专项检查记录表", "安全隐患整改通知单", "安全隐患整改回复单", "×××费用明细表", "养护工程安全检查考核表", "高速公路涉路及养护施工安全检查记录表", "安全管理全流程控制表", "工程开工安全生产条件报审表", "安全生产经费使用报审表", "危险性较大分部分项工程清单", "全员劳动用工登记台帐", "企业三级安全教育登记表", "一线工人业余学校教育表", "特种设备台帐", "特种设备使用前检查记录表", "起重设备试吊记录表", "安全生产物资购置台帐（        年）", "消防器材使用管理台帐（        年）", "安全技术交底台账", "安全技术交底", "安全技术交底记录表", "危险源防控表", "预警单元划分表", "应急预案登记台账", "应急预案演练台账", "应急预案演练记录表", "事故隐患排查与治理台帐", "施工安全日志", "夜间施工申请表", "安全生产重大险情快报表", "交通建设工程生产安全事故快报表"],
    "检测类用表": ["进场材料报验单", "土工标准试验审批表", "标准击实审批表", "材料料源审批表", "混凝土配合比审批表", "砂浆配合比审批表", "水泥净浆配合比审批表", "沥青混合料配合比审批表", "级配碎石配合比审批表", "合同段工地试验现场试验检测月报表", "合同段工地试验室原材试验检测月报表", "路基路面压实度试验检测汇总评定报告", "弯沉试验检测汇总评定报告", "水泥混凝土抗压强度试验检测汇总评定报告", "喷射混凝土抗压强度试验检测汇总评定报告", "水泥混凝土抗弯拉强度试验检测汇总评定报告", "砂浆抗压强度试验检测汇总评定报告", "路面结构层厚度试验检测汇总评定报告", "半刚性基层和底层材料强度评定", "土工试验台帐", "粗集料试验台帐", "细集料试验台帐", "水泥试验台帐", "水泥混凝土抗压强度试验台帐", "水泥浆抗折抗压强度试验台帐", "钢筋焊接试验台帐", "无机结合料稳定材料灰剂量试验台帐（EDTA滴定法）"],
    "质量检查用表": ["填方土边坡修复质量检验表", "土方路基修复质量检验表", "填石路基修复质量检验表", "路基注浆质量检验表", "管道铺设质量检验表", "检查(雨水)井整修、增设质量检验表", "土沟整修、增设质量检验表", "砌筑排水沟整修、增设质量检验表", "急流槽和跌水整修、增设质量检验表", "盲沟整修、增设质量检验表", "泄水孔整修、增设质量检验表", "砌体挡土墙修复质量检验表", "护面墙修复质量检验表", "锚杆、锚索质量检验表", "锥、护坡修复质量检验表", "水泥砂浆勾缝质量检验表", "边坡锚喷防护质量检验表", "边坡框架梁加注浆锚杆防护质量检验表", "加铺或铣刨重铺沥青混凝土面层质量检验表", "微表处和稀浆封层质量检验表", "碎石封层质量检验表", "就地热再生质量检验表", "厂拌热再生质量检验表", "含砂雾封层质量检验表", "超薄罩面质量检验表", "沥青路面局部挖补质量检验表", "沥青路面开槽灌缝质量检验表", "加铺水泥混凝土面层质量检验表", "水泥混凝土路面换板质量检验表", "水泥混凝土路面板底注浆质量检验表", "水泥混凝土路面刻槽质量检验表", "水泥混凝土路面碎石化质量检验表", "水泥混凝土路面就地发裂质量检验表", "沥青碎石基层翻修质量检验表", "厂拌冷再生、就地冷再生质量检验表", "全深式冷再生质量检验表", "稳定土基层和底基层翻修质量检验表", "稳定粒料基层和底基层翻修质量检验表", "级配碎石基层和底基层翻修质量检验表", "水泥混凝土桥面铺装维修质量检验表", "沥青混凝土桥面铺装质量检验表", "侧石、平石更换质量检验表", "沥青路缘石更换质量检验表", "人行道花砖更换质量检验表", "钢扶手更换质量检验表", "路肩修复质量检验表", "伸缩装置更换质量检验表", "伸缩缝橡胶条更换质量检验表", "支座安装质量检验表", "桥梁排水设施维修质量检验表", "构造物基坑质量检验表", "模板、支(拱)架安装现场质量检验表", "钢筋安装质量检验表", "钢筋网质量检验表", "衬砌钢筋质量检验表", "钢筋防锈蚀处理质量检验表", "梁体顶升质量检验表", "混凝土表面缺损修补质量检验表", "桥梁混凝土表面涂装质量检验表", "混凝士构件表面防护质量检验表", "裂缝表面封闭质量检验表", "裂缝灌浆质量检验表", "铰缝维修质量检验表", "植筋质量检验表", "锚栓锚固质量检验表", "钢筋混凝土构件增大截面质量检验表", "设置体外预应力质量检验表", "钢丝、钢绞线先张法质量检验表", "后张法质量检验表", "粘贴钢板质量检验表", "粘贴纤维复合材料质量检验表", "钢桥焊接加固质量检验表", "钢桥更换(增加)构件质量检验表", "钢结构下料切割面质量检验表", "钢结构零件矫正质量检验表", "钢结构涂装防护质量检验表", "高强螺栓更换质量检验表", "钢筋混凝土拱脱空注浆质量检验表", "钢管混凝土拱外包混凝土质量检验表", "更换吊杆、吊索质量检验表", "更换拱桥系杆质量检验表", "柔性系杆更换质量检验表", "主缆防护质量检验表", "斜拉桥换索及调索质量检验表", "斜拉索、吊杆防护套修补质量检验表", "接长与加宽盖梁、台帽质量检验表", "增设或更换挡块质量检验表", "墩身外包钢质量检验表", "钢花管注浆锚杆加固桥台质量检验表", "玻璃纤维套筒加固质量检验表", "套箍加固质量检验表", "钢筋混凝土挡块、钢挡板支架(防落梁装置)质量检验表", "拉杆连接(防落梁装置)质量检验表", "混凝土桩预制质量检验表", "钢管桩制作质量检验表", "压桩质量检验表", "混凝土桩身修补质量检验表", "涵洞接长质量检验表", "涵洞台身增大截面加固质量检验表", "地基注浆加固质量检验表", "混凝土涵管增大截面加固质量检验表", "拱涵主拱圈增大截面加固质量检验表", "一字墙和八字墙局部更换砌块质量检验表", "排水设施维修质量检验表", "人行道(检修道)质量检验表", "衬砌背面压(注)浆质量检验表", "喷射混凝土加固质量检验表", "喷射混凝土、模筑混凝土(隧道衬砌)加固质量检验表", "粘贴纤维复合材料(隧道衬砌)加固质量检验表", "粘贴钢板(隧道衬砌)加固质量检验表", "底部换填(隧底加固)质量检验表", "基底桩孔(隧底加固)质量检验表", "树根桩、钢管桩、高压旋喷桩基(隧底加固)质量检验表", "灰土桩基(隧底加固)质量检验表", "嵌(套)拱质量检验表", "混凝土衬砌更换质量检验表", "隧道衬砌修补质量检验表", "增设仰拱质量检验表", "埋管引排水质量检验表", "止水质量检验表", "隧道瓷砖修补质量检验表", "防火涂料修复质量检验表", "渗漏水处置质量检验表", "冻害处治质量检验表", "交通标志更换、增设质量检验表", "路面标线划设质量检验表", "里程碑、百米桩、界碑更换、增设质量检验表", "波形梁钢护栏更换、增设质量检验表", "混凝土护栏整修、增设质量检验表", "缆索护栏更换、增设质量检验表", "混凝土隔离墩更换、增设质量检验表", "隔离栅更换、增设质量检验表", "突起路标更换、增设质量检验表", "轮廓标更换、增设质量检验表", "防眩设施更换、增设质量检验表", "隔离栅和防落网更换、增设质量检验表", "金属框架声屏障更换、增设质量检验表", "栽植土补缺、更换质量检验表", "植物材料(乔木)更新、补缺质量检验表", "植物材料(灌木)更新、补缺质量检验表", "植物材料(球类)更新、补缺质量检验表", "植物材料(草块和草本地被)更新、补缺质量检验表", "乔木、灌木栽植质量检验表", "草坪、草本地被栽植质量检验表"],
    "施工、测量记录表": ["路堤填筑施工记录", "路基、路面压实沉降（高程法）检测记录", "锚孔钻造施工记录", "砌体工程施工记录", "薄壁管桩施工记录", "静力压桩施工记录", "预应力锚索（杆）注浆施工记录", "沥青路面裂缝处治施工记录", "沥青路面坑槽修补施工记录", "沥青路面热补施工记录", "沥青路面热补施工现场记录", "封层洒布施工记录", "水泥混凝土接缝保养及维修施工记录", "水泥混凝土路面接缝和钢筋施工记录", "水泥砼破损维修施工记录", "支座养护与维修记录", "伸缩装置养护与维修记录", "裂缝处置施工记录", "排水系统清理施工记录", "混凝土施工记录", "预应力锚索张拉记录", "预应力筋管道检测记录", "先张法预应力张拉施工记录", "后张法预应力张拉施工记录", "预应力筋孔道压浆施工记录", "绿化苗木修剪施工记录", "绿化苗病虫害防治施工记录", "×××（通用）施工记录", "养护任务影像记录表", "病害修复影像记录表", "路基沉降与水平位移观测记录", "平面控制点测量复核成果", "测量控制点交接表", "高程控制点测量复核成果", "路线水准点（复测）测量记录表", "水准高程误差配赋表", "四等水准测量记录表", "路基、构造物、路面施工增加水准点测量记录表", "施工放样（复核）测量记录表", "水准高程测量记录表", "隧道施工测量记录表", "横坡测量记录表", "平面位置测量记录表", "平整度测量记录表", "路线导线网、点（复测）测量记录表", "宽度、边坡测量记录表", "拱顶下沉量测记录表", "周边位移量测记录表", "路基高边坡位移监测表"],
    "质量评定用表": ["合同段工程质量鉴定表", "养护工程质量评定表", "填方土边坡修复养护单元工程质量检验评定表", "土方路基修复养护单元工程质量检验评定表", "填石路基修复养护单元工程质量检验评定表", "管道铺设 养护单元工程质量检验评定表", "管道铺设养护单元工程质量检验评定表", "检查（雨水）井整修、增设养护单元工程质量检验评定表", "土沟整修、增设养护单元工程质量检验评定表", "砌筑排水沟整修、增设养护单元工程质量检验评定表", "急流槽和跌水整修、增设养护单元工程质量检验评定表", "盲沟整修、增设养护单元工程质量检验评定表", "泄水孔整修、增设养护单元工程质量检验评定表", "砌体挡土墙修复养护单元工程质量检验评定表", "护面墙修复养护单元工程质量检验评定表", "锥、护坡修复养护单元工程质量检验评定表", "边坡锚喷防护养护单元工程质量检验评定表", "边坡框架梁加注浆锚杆防护养护单元工程质量检验评定表", "加铺或铣刨重铺沥青混凝土面层养护单元工程质量检验评定表", "微表处和稀浆封层养护单元工程质量检验评定表", "碎石封层养护单元工程质量检验评定表", "就地热再生养护单元工程质量检验评定表", "含砂雾封层养护单元工程质量检验评定表", "沥青路面局部挖补养护单元工程质量检验评定表", "沥青路面开槽灌缝养护单元工程质量检验评定表", "加铺水泥混凝土面层养护单元工程质量检验评定表", "水泥混凝土路面换板养护单元工程质量检验评定表", "水泥混凝土路面板底注浆养护单元工程质量检验评定表", "水泥混凝土路面刻槽养护单元工程质量检验评定表", "水泥混凝土路面碎石化养护单元工程质量检验评定表", "沥青碎石基层翻修养护单元工程质量检验评定表", "厂拌冷再生、就地冷再生养护单元工程质量检验评定表", "全深式冷再生养护单元工程质量检验评定表", "稳定土基层和底基层翻修养护单元工程质量检验评定表", "稳定粒料基层和底基层翻修养护单元工程质量检验评定表", "级配碎石基层和底基层翻修养护单元工程质量检验评定表", "水泥混凝土桥面铺装维修养护单元工程质量检验评定表", "沥青混凝土桥面铺装维修养护单元工程质量检验评定表", "伸缩装置更换养护单元工程质量检验评定表", "排水设施维修养护单元工程质量检验评定表", "梁体顶升养护单元工程质量检验评定表", "混凝土表面缺损修补养护单元工程质量检验评定表", "混凝土裂缝表面封闭养护单元工程质量检验评定表", "混凝土裂缝灌浆养护单元工程质量检验评定表", "植筋养护单元工程质量检验评定表", "钢筋混凝土构件增大截面养护单元工程质量检验评定表", "设置体外预应力养护单元工程质量检验评定表", "粘贴钢板养护单元工程质量检验评定表", "粘贴纤维复合材料养护单元工程质量检验评定表", "钢结构涂装防护养护单元工程质量检验评定表", "高强螺栓更换养护单元工程质量检验评定表", "钢管混凝土拱脱空注浆养护单元工程质量检验评定表", "钢管混凝土拱外包混凝土养护单元工程质量检验评定表", "更换吊杆、吊索养护单元工程质量检验评-定表", "更换拱桥系杆养护单元工程质量检验评定表", "斜拉桥换索及调索 养护单元工程质量检验评定表", "斜拉索、吊杆防护套修补养护单元工程质量检验评定表", "接长与加宽盖梁、台帽维修养护单元工程质量检验评定表", "增设或更换挡块养护单元工程质量检验评定表", "墩身外包钢养护单元工程质量检验评定表", "钢花管注浆锚杆加固桥台养护单元工程质量检验评定表", "混凝土桩预制养护单元工程质量检验评定表", "钢管桩制作养护单元工程质量检验评定表", "压桩养护单元工程质量检验评定表", "混凝土桩身修补养护单元工程质量检验评定表", "涵洞接长养护单元工程质量检验评定表", "涵洞台身增大截面加固养护单元工程质量检验评定表", "地基注浆加固养护单元工程质量检验评定表", "混凝土涵管增大截面加固 养护单元工程质量检验评定表", "拱涵主拱圈增大截面加固养护单元工程质量检验评定表", "一字墙和八字墙局部更换砌块 养护单元工程质量检验评定表", "排水设施维修 养护单元工程质量检验评定表", "人行道（检修道）维修 养护单元工程质量检验评定表", "衬砌背面压（注）浆养护单元工程质量检验评定表", "喷射混凝土加固养护单元工程质量检验评定表", "套（嵌）拱 养护单元工程质量检验评定表", "混凝土衬砌更换 养护单元工程质量检验评定表", "增设仰拱 养护单元工程质量检验评定表", "埋管引排水 养护单元工程质量检验评定表", "止水养护单元工程质量检验评定表", "冻害处治养护单元工程质量检验评定表", "交通标志更换、增设 养护单元工程质量检验评定表", "路面标线划设 养护单元工程质量检验评定表", "里程碑、百米桩和界碑更换、增设 养护单元工程质量检验评定表", "波形梁钢护栏更换、增设 养护单元工程质量检验评定表", "混凝土护栏整修、增设养护单元工程质量检验评定表", "缆索护栏更换、增设 养护单元工程质量检验评定表", "混凝土隔离墩更换、增设养护单元工程质量检验评定表", "隔离栏更换、增设养护单元工程质量检验评定表", "突起路标更换、增设养护单元工程质量检验评定表", "轮廓标更换、增设 养护单元工程质量检验评定表", "防眩设施更换、增设养护单元工程质量检验评定表", "隔离栅和防落网更换、增设养护单元工程质量检验评定表", "金属框架声屏障整修、增设养护单元工程质量检验评定表", "栽植土补缺、更换养护单元工程质量检验评定表", "植物材料（乔木）更新、补缺 养护单元工程质量检验评定表", "植物材料（灌木）更新、补缺 养护单元工程质量检验评定表", "植物材料（球类）更新、补缺 养护单元工程质量检验评定表", "植物材料（草块和草本地被）更新、补缺养护单元工程质量检验评定表", "乔木、灌木栽植养护单元工程质量检验评定表", "草坪、草本地被栽植养护单元工程质量检验评定表"]
}

# --- 3. 核心处理逻辑 ---

def create_reverse_mapping(mapping_dict):
    """
    创建一个从 "Sheet名" 到 "Excel文件名" 的反向映射，便于快速查找。
    """
    reverse_map = {}
    for excel_file, sheet_list in mapping_dict.items():
        for sheet_name in sheet_list:
            # 清理 sheet_name 中的空白字符，以提高匹配成功率
            cleaned_sheet_name = sheet_name.strip()
            if cleaned_sheet_name in reverse_map:
                print(f"警告：工作表名称 '{cleaned_sheet_name}' 在多个Excel文件定义中重复出现。")
                print(f"  - 将使用第一个找到的Excel文件: '{reverse_map[cleaned_sheet_name]}'")
            else:
                reverse_map[cleaned_sheet_name] = excel_file
    return reverse_map

def extract_tables_from_word(doc_path, sheet_to_excel_map):
    """
    从Word文档中提取表格。
    关键假设：表格的标题是其正上方紧邻的段落。
    """
    if not os.path.exists(doc_path):
        print(f"错误：Word文件未找到，请检查路径 '{doc_path}'")
        return {}

    print(f"正在读取Word文档: {doc_path}")
    doc = docx.Document(doc_path)

    # 使用 doc.element.body.iter() 来保证正确的顺序
    # Word文档的内部结构是段落(p)和表格(tbl)的序列
    # 我们需要找到一个表格(tbl)，然后看它前面的元素是不是段落(p)

    extracted_data = {}
    all_doc_elements = list(doc.element.body)

    # 遍历Word文档中的所有元素（段落和表格）
    for i, element in enumerate(all_doc_elements):
        # 检查当前元素是否为表格
        if element.tag.endswith('tbl'):
            # 如果是表格，检查它前面的元素是否为段落
            if i > 0 and all_doc_elements[i-1].tag.endswith('p'):
                # 获取表格对象和它前面的段落对象
                table_obj = doc.tables[[t._element for t in doc.tables].index(element)]
                paragraph_obj = doc.paragraphs[[p._element for p in doc.paragraphs].index(all_doc_elements[i-1])]

                # 将段落文字作为表格标题
                title = paragraph_obj.text.strip()

                if title in sheet_to_excel_map:
                    print(f"  - 找到匹配表格: '{title}'")
                    # 提取表格数据
                    table_data = []
                    for row in table_obj.rows:
                        row_data = [cell.text.strip() for cell in row.cells]
                        table_data.append(row_data)

                    # 存储数据
                    excel_file = sheet_to_excel_map[title]
                    if excel_file not in extracted_data:
                        extracted_data[excel_file] = {}

                    # 使用原始的sheet名称（未清理的）作为key
                    original_sheet_name = [s for s in EXCEL_SHEET_MAPPING[excel_file] if s.strip() == title][0]
                    extracted_data[excel_file][original_sheet_name] = table_data

    return extracted_data

def write_data_to_excel(output_dir, data_to_write):
    """
    将提取的数据写入到多个Excel文件中。
    """
    if not data_to_write:
        print("未找到任何可提取的表格数据。")
        return

    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    print(f"\n开始写入Excel文件到目录: '{output_dir}'")

    for excel_filename, sheets_data in data_to_write.items():
        output_path = os.path.join(output_dir, f"{excel_filename}.xlsx")
        print(f"  > 正在创建/更新Excel文件: {excel_filename}.xlsx")

        # 使用 ExcelWriter 来向同一个文件写入多个sheet
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for sheet_name, table_data in sheets_data.items():
                if not table_data:
                    print(f"    - 跳过空表格: '{sheet_name}'")
                    continue

                # 将列表的列表转换为DataFrame，通常第一行是表头
                # 如果表格没有表头，可以修改此处的逻辑
                try:
                    df = pd.DataFrame(table_data[1:], columns=table_data[0])
                except Exception:
                    # 如果第一行不能作为合法的表头（例如有合并单元格或重复项），则不使用表头
                    df = pd.DataFrame(table_data)
                    print(f"    * 警告：在工作表 '{sheet_name}' 中无法将第一行设为表头，将直接写入数据。")

                # 清理工作表名称，Excel对特殊字符有限制
                # Excel sheet name must be <= 31 chars, and not contain any of a specific set of chars
                safe_sheet_name = sheet_name.replace(':', '').replace('\\', '').replace('/', '').replace('?', '').replace('*', '').replace('[', '').replace(']', '')
                if len(safe_sheet_name) > 31:
                    safe_sheet_name = safe_sheet_name[:31]

                print(f"    - 正在写入工作表: '{safe_sheet_name}'")
                df.to_excel(writer, sheet_name=safe_sheet_name, index=False)

    print("\n所有操作完成！")

def main():
    """
    主执行函数
    """
    # 1. 创建反向映射，方便查找
    sheet_to_excel_map = create_reverse_mapping(EXCEL_SHEET_MAPPING)

    # 2. 从Word文档中提取所有匹配的表格数据
    # 返回的数据结构: {'Excel文件名': {'Sheet名': [[行1], [行2], ...]}, ...}
    data_to_write = extract_tables_from_word(WORD_FILE_PATH, sheet_to_excel_map)

    # 3. 将提取的数据写入Excel文件
    write_data_to_excel(OUTPUT_DIR, data_to_write)

    # 4. 报告未找到的表格
    print("\n--- 检查报告 ---")
    found_sheets = {sheet.strip() for sheets in data_to_write.values() for sheet in sheets.keys()}
    all_defined_sheets = set(sheet_to_excel_map.keys())
    missing_sheets = all_defined_sheets - found_sheets

    if missing_sheets:
        print(f"在Word文档中未能找到以下 {len(missing_sheets)} 个表格的标题：")
        for i, sheet in enumerate(sorted(list(missing_sheets)), 1):
            print(f"  {i}. {sheet}")
        print("\n请检查Word文档中这些表格的标题是否与定义完全一致（包括空格和特殊字符）。")
    else:
        print("所有在定义中的表格都已成功找到并处理。")


if __name__ == "__main__":
    main()